#include "ebtn_driver.h"

/* ����Ĭ������ */
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(
    20,     // time_debounce: �����ȶ� 20ms
    20,     // time_debounce_release: �ͷ��ȶ� 20ms
    50,     // time_click_pressed_min: ��̵������� 50ms������Ϊ0�������������Сֵ
    500,    // time_click_pressed_max: ��������� 500ms (�������㵥��)������Ϊ0xFFFF��������������ֵ���������ֳ����Ͱ����¼���
    200,    // time_click_multi_max: ��ε�������� 300ms (���ε��������������¼���)
    1000,    // time_keepalive_period: �����¼����� 500ms (���³��� 500ms ��ÿ 500ms ����һ��)
    5       // max_consecutive: ���֧�� 5 ����
);

typedef enum
{
    USER_BUTTON_1 = 1,
    USER_BUTTON_2 = 2,
    USER_BUTTON_3 = 3,
    USER_BUTTON_4 = 4,
    USER_BUTTON_MAX,

    USER_BUTTON_COMBO_MAX,
} user_button_t;

/* 2. ���徲̬�����б� */
// ��: EBTN_BUTTON_INIT(����ID, ����ָ��)
static ebtn_btn_t static_buttons[] = {
        EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),  // PE0 - KEY1
        EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),  // PE1 - KEY2
        EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),  // PE2 - KEY3
        EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),  // PE3 - KEY4
};

// /* 3. ���徲̬��ϰ����б� (��ѡ) */
// // ��: EBTN_BUTTON_COMBO_INIT(����ID, ����ָ��)
// ebtn_btn_combo_t static_combos[] = {
//     // ���� KEY1+KEY2 ��ϼ�
//     EBTN_BUTTON_COMBO_INIT(USER_BUTTON_COMBO_0, &defaul_ebtn_param), // ��ϼ�, ID=USER_BUTTON_COMBO_0 (��������ͨ����ID��ͬ)
  
//     EBTN_BUTTON_COMBO_INIT(USER_BUTTON_COMBO_1, &defaul_ebtn_param), 
  
//     EBTN_BUTTON_COMBO_INIT(USER_BUTTON_COMBO_2, &defaul_ebtn_param), 
// };

/* 1. ʵ�ֻ�ȡ����״̬�Ļص����� */
// ����ԭ��: uint8_t (*ebtn_get_state_fn)(struct ebtn_btn *btn);
uint8_t my_get_key_state(struct ebtn_btn *btn) {
    // ���ݴ���İ�ťʵ���е� key_id �ж����ĸ���������
    switch (btn->key_id) {
        case 1: // �����ȡ KEY1 ��״̬
            // ���谴��Ϊ�ߵ�ƽ (���� 1 ��������)
            return (HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin) == GPIO_PIN_RESET);
        // ... �������Ӹ��ఴ���Ķ�ȡ�߼� ...
        case 2:
          return (HAL_GPIO_ReadPin(KEY2_GPIO_Port, KEY2_Pin) == GPIO_PIN_RESET);
        case 3:
          return (HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin) == GPIO_PIN_RESET);
        case 4:
          return (HAL_GPIO_ReadPin(KEY4_GPIO_Port, KEY4_Pin) == GPIO_PIN_RESET);
        default:
            // ���ڿ��ڲ�������ϼ������������δ֪�� key_id����ȫ������� 0 (δ����)
            return 0;
    }
    // ע�⣺����ֵ 1 ��ʾ "�/����"��0 ��ʾ "�ǻ/�ͷ�"
}

int Ebtn_Init(void)
{
  // 初始化 ebtn 库
    int init_ok = ebtn_init(
        static_buttons,                 // 静态按钮数组指针
        EBTN_ARRAY_SIZE(static_buttons), // 静态按钮数量 (宏函数)
        NULL,                           // 静态组合按钮数组指针 (如果没有，则 NULL, 0)
        0,                              // 静态组合按钮数量 (如果没有，则 0)
        my_get_key_state,               // 按键状态获取回调函数
        my_handle_key_event             // 按键事件处理回调函数
    );

    if (!init_ok) {
        // 初始化失败，这里可以处理错误
        my_printf(&huart1, "Button Init Failed!\r\n");
        return -1; // 或者进行其他错误处理
    }

    // 设置组合键优先等待模式，防止组合键与单键冲突
    ebtn_set_config(EBTN_CFG_COMBO_PRIORITY);

    my_printf(&huart1, "Button System Initialized - 4 Keys Ready\r\n");
    return 0;
}
