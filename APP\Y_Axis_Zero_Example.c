/**
 * @file Y_Axis_Zero_Example.c
 * @brief Y轴零点设置使用示例
 * <AUTHOR>
 * @date 2024
 * 
 * 使用说明：
 * 1. 手动将Y轴移动到想要的零点位置
 * 2. 调用 StepMotor_Y_Set_Current_As_Zero() 设置当前位置为零点
 * 3. 每次上电后调用 StepMotor_Y_Return_To_Zero() 自动回到零点
 */

#include "StepMotor_app.h"
#include "usart.h"

/**
 * @brief Y轴零点设置示例函数
 * @note 在main函数中调用此函数来设置Y轴零点
 */
void Y_Axis_Zero_Setup_Example(void)
{
    my_printf(&huart1, "=== Y轴零点设置示例 ===\r\n");
    
    // 步骤1：等待用户手动调整Y轴到想要的位置
    my_printf(&huart1, "请手动将Y轴移动到想要的零点位置，然后按任意键继续...\r\n");
    // 这里可以添加等待用户输入的代码
    
    // 步骤2：将当前位置设置为零点
    my_printf(&huart1, "正在将当前Y轴位置设置为零点...\r\n");
    StepMotor_Y_Set_Current_As_Zero();
    
    // 等待一段时间确保设置完成
    HAL_Delay(500);
    
    my_printf(&huart1, "Y轴零点设置完成!\r\n");
    my_printf(&huart1, "现在每次上电后调用 StepMotor_Y_Return_To_Zero() 即可自动回到此位置\r\n");
}

/**
 * @brief 上电自动回零示例函数
 * @note 在系统初始化完成后调用此函数，Y轴将自动回到零点
 */
void Y_Axis_Auto_Return_Zero_Example(void)
{
    my_printf(&huart1, "=== Y轴自动回零 ===\r\n");
    
    // 触发Y轴回零运动
    StepMotor_Y_Return_To_Zero();
    
    // 等待回零完成（可选）
    my_printf(&huart1, "等待Y轴回零完成...\r\n");
    
    // 检查电机是否到位
    uint32_t timeout = 0;
    while (StepMotor_Check_Ready() != 1 && timeout < 5000) // 最多等待5秒
    {
        HAL_Delay(10);
        timeout += 10;
    }
    
    if (timeout >= 5000)
    {
        my_printf(&huart1, "Y轴回零超时!\r\n");
    }
    else
    {
        my_printf(&huart1, "Y轴已成功回到零点位置!\r\n");
    }
}

/**
 * @brief 在main.c中的使用示例
 * @note 将以下代码添加到您的main.c文件中
 */
void Main_Usage_Example(void)
{
    /*
    // 在main函数中的使用示例：
    
    int main(void)
    {
        // ... 系统初始化代码 ...
        
        // 初始化步进电机
        StepMotor_Init();
        
        // 方式1：首次设置零点（只需要执行一次）
        // 当您想要重新设置零点时才调用这个函数
        // Y_Axis_Zero_Setup_Example();
        
        // 方式2：每次上电自动回零（推荐在系统启动时调用）
        Y_Axis_Auto_Return_Zero_Example();
        
        while (1)
        {
            // ... 主循环代码 ...
        }
    }
    */
}

/**
 * @brief 简化版本 - 直接在main.c中使用的代码
 */
void Simple_Usage_In_Main(void)
{
    /*
    // 在main.c中直接使用的简化代码：
    
    // 1. 设置零点（只在需要重新设置时使用）
    // StepMotor_Y_Set_Current_As_Zero();
    
    // 2. 每次上电自动回零（推荐）
    StepMotor_Y_Return_To_Zero();
    
    // 3. 等待回零完成（可选）
    while (StepMotor_Check_Ready() != 1)
    {
        HAL_Delay(10);
    }
    my_printf(&huart1, "Y轴已回到零点!\r\n");
    */
}
