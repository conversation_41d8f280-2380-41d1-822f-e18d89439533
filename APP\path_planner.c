/**
 * @file path_planner.c
 * @brief 智能路径规划系统实现
 * <AUTHOR> Code
 */

#include "path_planner.h"
#include <math.h>
#include <stdlib.h>

/* 全局路径规划器实例 */
static PathPlanner_t g_path_planner;

/**
 * @brief 初始化路径规划器
 */
void PathPlanner_Init(void)
{
    g_path_planner.point_count = 0;
    g_path_planner.current_index = 0;
    g_path_planner.strategy = PATH_DIRECT;
    g_path_planner.is_active = false;
    g_path_planner.path_complete = true;
    g_path_planner.last_update_time = 0;
    
    // 清空路径点数组
    for (int i = 0; i < MAX_PATH_POINTS; i++) {
        g_path_planner.points[i].x = 0;
        g_path_planner.points[i].y = 0;
        g_path_planner.points[i].speed_percent = 60;
        g_path_planner.points[i].hold_time_ms = 0;
    }
}

/**
 * @brief 计算两点间距离
 */
static float Calculate_Distance(int16_t x1, int16_t y1, int16_t x2, int16_t y2)
{
    float dx = x2 - x1;
    float dy = y2 - y1;
    return sqrt(dx * dx + dy * dy);
}

/**
 * @brief 选择最优路径策略
 */
static PathStrategy_t Select_Path_Strategy(int16_t dx, int16_t dy, float distance)
{
    int16_t abs_dx = abs(dx);
    int16_t abs_dy = abs(dy);
    
    // 小移动：直接路径
    if (distance < MEDIUM_MOVE_THRESHOLD) {
        return PATH_DIRECT;
    }
    
    // 大移动：分析最优策略
    if (distance > LARGE_MOVE_THRESHOLD) {
        // 如果一个轴的移动远大于另一个轴，使用L型路径
        if (abs_dx > abs_dy * 2) {
            return PATH_L_SHAPED;       // 先X后Y
        } else if (abs_dy > abs_dx * 2) {
            return PATH_REVERSE_L;      // 先Y后X
        } else {
            return PATH_STAGED;         // 分段直线路径
        }
    }
    
    // 中等移动：根据角度选择
    float angle = atan2(abs_dy, abs_dx) * 180.0f / 3.14159f;
    if (angle > 60 || angle < 30) {
        return PATH_L_SHAPED;
    } else {
        return PATH_DIRECT;
    }
}

/**
 * @brief 生成直接路径
 */
static void Generate_Direct_Path(int16_t current_x, int16_t current_y, 
                                int16_t target_x, int16_t target_y)
{
    g_path_planner.points[0].x = target_x;
    g_path_planner.points[0].y = target_y;
    g_path_planner.points[0].speed_percent = 60;
    g_path_planner.points[0].hold_time_ms = 100;
    g_path_planner.point_count = 1;
}

/**
 * @brief 生成分段路径
 */
static void Generate_Staged_Path(int16_t current_x, int16_t current_y, 
                                int16_t target_x, int16_t target_y)
{
    float distance = Calculate_Distance(current_x, current_y, target_x, target_y);
    int segments = (int)(distance / PATH_SEGMENT_SIZE) + 1;
    
    if (segments > MAX_PATH_POINTS - 1) {
        segments = MAX_PATH_POINTS - 1;
    }
    
    float dx_step = (float)(target_x - current_x) / segments;
    float dy_step = (float)(target_y - current_y) / segments;
    
    for (int i = 0; i < segments; i++) {
        g_path_planner.points[i].x = current_x + (int16_t)(dx_step * (i + 1));
        g_path_planner.points[i].y = current_y + (int16_t)(dy_step * (i + 1));
        g_path_planner.points[i].speed_percent = (i == segments - 1) ? 40 : 70; // 最后一段减速
        g_path_planner.points[i].hold_time_ms = (i == segments - 1) ? 200 : 50;
    }
    
    g_path_planner.point_count = segments;
}

/**
 * @brief 生成L型路径
 */
static void Generate_L_Path(int16_t current_x, int16_t current_y, 
                           int16_t target_x, int16_t target_y, bool x_first)
{
    if (x_first) {
        // 先移动X轴
        g_path_planner.points[0].x = target_x;
        g_path_planner.points[0].y = current_y;
        g_path_planner.points[0].speed_percent = 65;
        g_path_planner.points[0].hold_time_ms = 150;
        
        // 再移动Y轴
        g_path_planner.points[1].x = target_x;
        g_path_planner.points[1].y = target_y;
        g_path_planner.points[1].speed_percent = 55;
        g_path_planner.points[1].hold_time_ms = 200;
    } else {
        // 先移动Y轴
        g_path_planner.points[0].x = current_x;
        g_path_planner.points[0].y = target_y;
        g_path_planner.points[0].speed_percent = 65;
        g_path_planner.points[0].hold_time_ms = 150;
        
        // 再移动X轴
        g_path_planner.points[1].x = target_x;
        g_path_planner.points[1].y = target_y;
        g_path_planner.points[1].speed_percent = 55;
        g_path_planner.points[1].hold_time_ms = 200;
    }
    
    g_path_planner.point_count = 2;
}

/**
 * @brief 生成曲线路径(简化为多段直线)
 */
static void Generate_Curved_Path(int16_t current_x, int16_t current_y, 
                                int16_t target_x, int16_t target_y)
{
    // 简化的曲线路径：通过中间点创建平滑路径
    int16_t mid_x = (current_x + target_x) / 2;
    int16_t mid_y = (current_y + target_y) / 2;
    
    // 添加一些偏移创建弧形效果
    int16_t dx = target_x - current_x;
    int16_t dy = target_y - current_y;
    mid_x += dy / 4;  // 垂直偏移
    mid_y -= dx / 4;  // 水平偏移
    
    g_path_planner.points[0].x = mid_x;
    g_path_planner.points[0].y = mid_y;
    g_path_planner.points[0].speed_percent = 70;
    g_path_planner.points[0].hold_time_ms = 100;
    
    g_path_planner.points[1].x = target_x;
    g_path_planner.points[1].y = target_y;
    g_path_planner.points[1].speed_percent = 50;
    g_path_planner.points[1].hold_time_ms = 150;
    
    g_path_planner.point_count = 2;
}

/**
 * @brief 分析并生成路径
 * @param current_x 当前X坐标
 * @param current_y 当前Y坐标
 * @param target_x 目标X坐标
 * @param target_y 目标Y坐标
 * @return true:成功生成路径；false:失败
 */
bool PathPlanner_AnalyzePath(int16_t current_x, int16_t current_y, 
                             int16_t target_x, int16_t target_y)
{
    // 计算移动向量和距离
    int16_t dx = target_x - current_x;
    int16_t dy = target_y - current_y;
    float distance = Calculate_Distance(current_x, current_y, target_x, target_y);
    
    // 如果距离很小，不需要路径规划
    if (distance < 3.0f) {
        g_path_planner.is_active = false;
        g_path_planner.path_complete = true;
        return false;
    }
    
    // 选择路径策略
    g_path_planner.strategy = Select_Path_Strategy(dx, dy, distance);
    
    // 根据策略生成路径
    switch (g_path_planner.strategy) {
        case PATH_DIRECT:
            Generate_Direct_Path(current_x, current_y, target_x, target_y);
            break;
            
        case PATH_STAGED:
            Generate_Staged_Path(current_x, current_y, target_x, target_y);
            break;
            
        case PATH_L_SHAPED:
            Generate_L_Path(current_x, current_y, target_x, target_y, true);
            break;
            
        case PATH_REVERSE_L:
            Generate_L_Path(current_x, current_y, target_x, target_y, false);
            break;
            
        case PATH_CURVED:
            Generate_Curved_Path(current_x, current_y, target_x, target_y);
            break;
            
        default:
            Generate_Direct_Path(current_x, current_y, target_x, target_y);
            break;
    }
    
    // 激活路径规划
    g_path_planner.current_index = 0;
    g_path_planner.is_active = true;
    g_path_planner.path_complete = false;
    g_path_planner.last_update_time = HAL_GetTick();
    
    return true;
}

/**
 * @brief 获取下一个目标点
 * @param next_x 输出下一个目标X坐标
 * @param next_y 输出下一个目标Y坐标
 * @return true:获取成功；false:路径完成或无效
 */
bool PathPlanner_GetNextTarget(int16_t *next_x, int16_t *next_y)
{
    if (!g_path_planner.is_active || g_path_planner.path_complete) {
        return false;
    }
    
    if (g_path_planner.current_index >= g_path_planner.point_count) {
        g_path_planner.path_complete = true;
        g_path_planner.is_active = false;
        return false;
    }
    
    // 返回当前目标点
    *next_x = g_path_planner.points[g_path_planner.current_index].x;
    *next_y = g_path_planner.points[g_path_planner.current_index].y;
    
    return true;
}

/**
 * @brief 推进到下一个路径点
 * @return true:成功推进；false:已到达路径终点
 */
bool PathPlanner_AdvanceToNext(void)
{
    if (!g_path_planner.is_active) {
        return false;
    }
    
    g_path_planner.current_index++;
    
    if (g_path_planner.current_index >= g_path_planner.point_count) {
        g_path_planner.path_complete = true;
        g_path_planner.is_active = false;
        return false;
    }
    
    return true;
}

/**
 * @brief 检查路径是否完成
 */
bool PathPlanner_IsComplete(void)
{
    return g_path_planner.path_complete || !g_path_planner.is_active;
}

/**
 * @brief 重置路径规划器
 */
void PathPlanner_Reset(void)
{
    g_path_planner.is_active = false;
    g_path_planner.path_complete = true;
    g_path_planner.current_index = 0;
    g_path_planner.point_count = 0;
}

/**
 * @brief 获取当前路径策略
 */
PathStrategy_t PathPlanner_GetStrategy(void)
{
    return g_path_planner.strategy;
}

/**
 * @brief 获取路径执行进度
 * @return 进度百分比(0-100)
 */
uint8_t PathPlanner_GetProgress(void)
{
    if (g_path_planner.point_count == 0) {
        return 100;
    }
    
    return (g_path_planner.current_index * 100) / g_path_planner.point_count;
}

/**
 * @brief 获取当前路径点信息(调试用)
 */
PathPoint_t* PathPlanner_GetCurrentPoint(void)
{
    if (!g_path_planner.is_active || 
        g_path_planner.current_index >= g_path_planner.point_count) {
        return NULL;
    }
    
    return &g_path_planner.points[g_path_planner.current_index];
}