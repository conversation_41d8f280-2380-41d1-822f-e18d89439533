/**
 * @file trajectory_example.c
 * @brief 轨迹插值使用示例
 * <AUTHOR> Code
 */
 
#include "StepMotor_app.h"

/**
 * @brief 轨迹插值使用示例
 * 演示如何使用轨迹插值功能实现丝滑的斜线运动
 */
void Trajectory_Example_Usage(void)
{
    // 1. 初始化步进电机和轨迹系统
    StepMotor_Init();
    StepMotor_Trajectory_Init();
    
    // 2. 规划一个正方形轨迹
    // 起点(0,0) -> (100,0) -> (100,100) -> (0,100) -> (0,0)
    StepMotor_Move_To_Point(100, 0, 50);    // 以50%速度移动到右上
    StepMotor_Move_To_Point(100, 100, 70);  // 以70%速度移动到右下
    StepMotor_Move_To_Point(0, 100, 60);    // 以60%速度移动到左下
    StepMotor_Move_To_Point(0, 0, 40);      // 以40%速度返回原点
    
    // 3. 在主循环中执行轨迹(或在定时器中断中调用)
    while (!StepMotor_Is_Trajectory_Complete()) {
        StepMotor_Trajectory_Execute();
        // 其他任务处理...
        HAL_Delay(1);  // 小延时避免CPU占用过高
    }
}

/**
 * @brief 激光追踪应用示例
 * 模拟红色激光追踪绿色激光的场景
 */
void Laser_Tracking_Example(void)
{
    // 初始化系统
    StepMotor_Init();
    StepMotor_Trajectory_Init();
    
    // 模拟绿色激光位置序列(可以从视觉处理模块获得)
    int16_t green_laser_positions[][2] = {
        {0, 0},      // 起始位置
        {50, 30},    // 目标1
        {80, 60},    // 目标2
        {40, 90},    // 目标3
        {10, 50},    // 目标4
        {30, 20}     // 目标5
    };
    
    int position_count = sizeof(green_laser_positions) / sizeof(green_laser_positions[0]);
    
    // 依次追踪每个目标点
    for (int i = 0; i < position_count; i++) {
        int16_t target_x = green_laser_positions[i][0];
        int16_t target_y = green_laser_positions[i][1];
        
        // 根据距离调整追踪速度
        uint16_t tracking_speed = 60;  // 基础追踪速度60%
        
        // 添加到轨迹缓冲区
        if (!StepMotor_Move_To_Point(target_x, target_y, tracking_speed)) {
            // 缓冲区满，等待执行
            while (trajectory_buffer.count > TRAJECTORY_BUFFER_SIZE / 2) {
                StepMotor_Trajectory_Execute();
                HAL_Delay(1);
            }
            // 重试添加
            StepMotor_Move_To_Point(target_x, target_y, tracking_speed);
        }
    }
    
    // 等待所有轨迹执行完成
    while (!StepMotor_Is_Trajectory_Complete()) {
        StepMotor_Trajectory_Execute();
        HAL_Delay(1);
    }
}

/**
 * @brief 定时器中断服务函数示例
 * 建议在20ms定时器中断中调用轨迹执行函数
 */
void TIM_Trajectory_IRQHandler(void)
{
    static uint32_t counter = 0;
    
    // 每20ms执行一次轨迹更新
    if (++counter >= 20) {  // 假设定时器中断1ms一次
        counter = 0;
        StepMotor_Trajectory_Execute();
    }
}

/**
 * @brief 实时轨迹更新示例
 * 适用于需要实时响应的应用场景
 */
void Real_Time_Tracking_Example(void)
{
    StepMotor_Init();
    StepMotor_Trajectory_Init();
    
    while (1) {
        // 假设从传感器或视觉模块获取目标位置
        int16_t detected_x = 0;  // 从检测模块获取
        int16_t detected_y = 0;  // 从检测模块获取
        
        // 清空之前的轨迹，立即追踪新目标
        StepMotor_Clear_Trajectory();
        
        // 添加新的追踪目标
        StepMotor_Move_To_Point(detected_x, detected_y, 80);
        
        // 执行轨迹
        StepMotor_Trajectory_Execute();
        
        HAL_Delay(50);  // 50ms更新频率
    }
}