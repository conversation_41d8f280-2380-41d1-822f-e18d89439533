# Y轴零点设置使用说明

## 概述

本文档说明如何使用新增的Y轴零点设置功能，实现将Y轴当前位置设置为零点，并在每次上电时自动回到该位置。

## 功能特点

- ✅ **简单易用**：只需调用两个函数即可完成零点设置和自动回零
- ✅ **基于Emm_V5库**：使用官方驱动库，稳定可靠
- ✅ **串口4通讯**：与Y轴步进电机通过串口4进行通讯
- ✅ **自动回零**：每次上电后可自动回到设定的零点位置

## 新增函数说明

### 1. StepMotor_Y_Set_Current_As_Zero()

**功能**：将Y轴当前位置设置为零点

**使用场景**：当您手动将Y轴调整到想要的零点位置后，调用此函数保存该位置为零点

**示例**：
```c
// 将当前Y轴位置设置为零点
StepMotor_Y_Set_Current_As_Zero();
```

### 2. StepMotor_Y_Return_To_Zero()

**功能**：Y轴自动回到零点位置

**使用场景**：每次系统上电后，调用此函数让Y轴自动回到之前设置的零点位置

**示例**：
```c
// Y轴自动回零
StepMotor_Y_Return_To_Zero();
```

## 使用步骤

### 第一步：设置零点（只需执行一次）

1. 手动将Y轴移动到您想要的零点位置
2. 在代码中调用零点设置函数：

```c
// 将当前位置设置为零点
StepMotor_Y_Set_Current_As_Zero();
```

### 第二步：每次上电自动回零

在您的main.c文件中，系统初始化完成后添加：

```c
int main(void)
{
    // ... 系统初始化代码 ...
    
    // 初始化步进电机
    StepMotor_Init();
    
    // 每次上电自动回零
    StepMotor_Y_Return_To_Zero();
    
    // 可选：等待回零完成
    while (StepMotor_Check_Ready() != 1)
    {
        HAL_Delay(10);
    }
    my_printf(&huart1, "Y轴已回到零点!\r\n");
    
    while (1)
    {
        // ... 主循环代码 ...
    }
}
```

## 完整使用示例

```c
#include "StepMotor_app.h"

void Y_Axis_Zero_Demo(void)
{
    // 步骤1：设置零点（首次使用或需要重新设置时）
    my_printf(&huart1, "请将Y轴移动到想要的零点位置...\r\n");
    HAL_Delay(5000); // 给用户5秒时间调整位置
    
    my_printf(&huart1, "设置当前位置为零点...\r\n");
    StepMotor_Y_Set_Current_As_Zero();
    HAL_Delay(500);
    
    // 步骤2：测试自动回零功能
    my_printf(&huart1, "移动Y轴到其他位置进行测试...\r\n");
    StepMotor_Move_Pulses(0, 1000); // 移动Y轴1000个脉冲
    HAL_Delay(2000);
    
    my_printf(&huart1, "开始自动回零...\r\n");
    StepMotor_Y_Return_To_Zero();
    
    // 等待回零完成
    while (StepMotor_Check_Ready() != 1)
    {
        HAL_Delay(10);
    }
    
    my_printf(&huart1, "Y轴已成功回到零点位置!\r\n");
}
```

## 技术细节

### 通讯配置
- **串口**：UART4 (huart4)
- **波特率**：115200
- **电机地址**：0x01
- **通讯协议**：Emm_V5协议

### 底层实现
- 使用 `Emm_V5_Reset_CurPos_To_Zero()` 设置零点
- 使用 `Emm_V5_Origin_Trigger_Return()` 触发回零运动
- 自动同步本地脉冲计数器

### 注意事项

1. **首次设置**：零点设置功能只需要执行一次，设置后会保存在电机内部
2. **上电回零**：每次系统上电后调用回零函数，Y轴会自动回到设定位置
3. **运动检测**：可使用 `StepMotor_Check_Ready()` 检测回零运动是否完成
4. **超时处理**：建议添加超时检测，避免无限等待

## 故障排除

### 问题1：回零运动没有响应
- 检查串口4连接是否正常
- 确认电机地址设置正确（0x01）
- 验证电机是否已使能

### 问题2：回零位置不准确
- 重新执行零点设置步骤
- 检查机械结构是否有松动
- 确认脉冲计数是否正确

### 问题3：通讯超时
- 检查波特率设置（115200）
- 验证串口4硬件连接
- 确认电机电源供应正常

## 版权信息

**开发者**：米醋电子工作室  
**版本**：v1.0  
**日期**：2024年
