#include "MyDefine.h"
#include "StepMotor_app.h"
#include <math.h>
#include <stdlib.h>

/* 轨迹系统全局变量 */
static TrajectoryBuffer_t trajectory_buffer;
static TrajectoryPoint_t current_position = {0, 0, 0};

/**
 * @brief 初始化轨迹系统
 */
void StepMotor_Trajectory_Init(void)
{
    trajectory_buffer.head = 0;
    trajectory_buffer.tail = 0;
    trajectory_buffer.count = 0;
    trajectory_buffer.is_executing = false;
    current_position.x = 0;
    current_position.y = 0;
    current_position.speed = 0;
}

/**
 * @brief Bresenham直线算法生成轨迹插值
 * @param x0 起始点X坐标
 * @param y0 起始点Y坐标
 * @param x1 终止点X坐标
 * @param y1 终止点Y坐标
 * @param speed_percent 速度百分比
 * @return 添加的点数
 */
static uint16_t StepMotor_Generate_Line_Points(int16_t x0, int16_t y0, int16_t x1, int16_t y1, uint16_t speed_percent)
{
    int16_t dx = abs(x1 - x0);
    int16_t dy = abs(y1 - y0);
    int16_t x_step = (x0 < x1) ? 1 : -1;
    int16_t y_step = (y0 < y1) ? 1 : -1;
    int16_t err = dx - dy;
    int16_t x = x0, y = y0;
    uint16_t point_count = 0;
    
    while (1) {
        // 将点添加到缓冲区
        if (trajectory_buffer.count < TRAJECTORY_BUFFER_SIZE) {
            uint16_t tail_index = trajectory_buffer.tail;
            trajectory_buffer.points[tail_index].x = x;
            trajectory_buffer.points[tail_index].y = y;
            trajectory_buffer.points[tail_index].speed = speed_percent;
            
            trajectory_buffer.tail = (trajectory_buffer.tail + 1) % TRAJECTORY_BUFFER_SIZE;
            trajectory_buffer.count++;
            point_count++;
        } else {
            // 缓冲区满，停止添加
            break;
        }
        
        // 检查是否到达终止点
        if (x == x1 && y == y1) break;
        
        // Bresenham算法下一步处理
        int16_t e2 = 2 * err;
        if (e2 > -dy) {
            err -= dy;
            x += x_step;
        }
        if (e2 < dx) {
            err += dx;
            y += y_step;
        }
    }
    
    return point_count;
}

/**
 * @brief 移动到指定点，使用轨迹插值
 * @param target_x 目标X坐标
 * @param target_y 目标Y坐标
 * @param speed_percent 速度百分比
 * @return true:成功添加轨迹；false:失败
 */
bool StepMotor_Move_To_Point(int16_t target_x, int16_t target_y, uint16_t speed_percent)
{
    // 限制范围检查
    if (speed_percent > 100) speed_percent = 100;
    
    // 使用Bresenham算法生成轨迹点
    uint16_t points_added = StepMotor_Generate_Line_Points(
        current_position.x, current_position.y,
        target_x, target_y, speed_percent
    );
    
    if (points_added > 0) {
        // 更新当前位置
        current_position.x = target_x;
        current_position.y = target_y;
        current_position.speed = speed_percent;
        return true;
    }
    
    return false;
}

/**
 * @brief 轨迹执行函数(需在定时器中调用)
 */
void StepMotor_Trajectory_Execute(void)
{
    static uint32_t last_execute_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 检查时间间隔
    if (current_time - last_execute_time < TRAJECTORY_STEP_TIME) {
        return;
    }
    last_execute_time = current_time;
    
    // 如果没有轨迹点待执行
    if (trajectory_buffer.count == 0) {
        trajectory_buffer.is_executing = false;
        return;
    }
    
    trajectory_buffer.is_executing = true;
    
    // 获取下一个轨迹点
    uint16_t head_index = trajectory_buffer.head;
    TrajectoryPoint_t* point = &trajectory_buffer.points[head_index];
    
    // 计算位置控制
    int8_t x_percent = 0, y_percent = 0;
    
    // 计算从当前位置到下一个位置的方向
    if (trajectory_buffer.count > 1) {
        // 获取下一个点
        uint16_t next_index = (trajectory_buffer.head + 1) % TRAJECTORY_BUFFER_SIZE;
        TrajectoryPoint_t* next_point = &trajectory_buffer.points[next_index];
        
        // 计算方向向量
        int16_t dx = next_point->x - point->x;
        int16_t dy = next_point->y - point->y;
        
        // 归一化并应用速度方向
        if (dx != 0 || dy != 0) {
            float magnitude = sqrt(dx*dx + dy*dy);
            x_percent = (int8_t)((dx / magnitude) * point->speed);
            y_percent = (int8_t)((dy / magnitude) * point->speed);
        }
    }
    
    // 设置电机速度
    StepMotor_Set_Speed(x_percent, y_percent);
    
    // 移除已处理的轨迹点
    trajectory_buffer.head = (trajectory_buffer.head + 1) % TRAJECTORY_BUFFER_SIZE;
    trajectory_buffer.count--;
}

/**
 * @brief 检查轨迹是否执行完成
 * @return true:执行完成；false:正在执行
 */
bool StepMotor_Is_Trajectory_Complete(void)
{
    return (trajectory_buffer.count == 0 && !trajectory_buffer.is_executing);
}

/**
 * @brief 清空轨迹缓冲区
 */
void StepMotor_Clear_Trajectory(void)
{
    trajectory_buffer.head = 0;
    trajectory_buffer.tail = 0;
    trajectory_buffer.count = 0;
    trajectory_buffer.is_executing = false;
    StepMotor_Stop();
}