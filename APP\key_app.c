#include "key_app.h"

void Key_Init()
{
    Ebtn_Init();
}

void Key_Task()
{
    ebtn_process(HAL_GetTick());
}

/* 按键事件处理的回调函数 */
// 函数原型: void (*ebtn_evt_fn)(struct ebtn_btn *btn, ebtn_evt_t evt);
void my_handle_key_event(struct ebtn_btn *btn, ebtn_evt_t evt) 
{
    uint16_t key_id = btn->key_id;                 // 获取触发事件的按键 ID
    
    switch (evt) 
    {
        case EBTN_EVT_ONPRESS: // 按下事件 (按键成功后触发一次)
            // 根据按键ID控制对应的LED
            switch(key_id)
            {
                case 1: // PE0 - KEY1 控制 LED1
                    led_buf[0] ^= 1;  // 翻转LED1状态
								SaveCoordinatesToFlash(myCoordinates, 2);//保存数据
                    my_printf(&huart1, "KEY1 Pressed - LED1 Toggle\r\n");
                    break;
                case 2: // PE1 - KEY2 控制 LED2  
                    led_buf[1] ^= 1;  // 翻转LED2状态
								StepMotor_Move_Pulses_Wait(X_PULSE,Y_PULSE,3000);//控制步进电机
                    my_printf(&huart1, "KEY2 Pressed - LED2 Toggle\r\n");
                    break;
                case 3: // PE2 - KEY3 控制 LED3
                    led_buf[2] ^= 1;  // 翻转LED3状态
                    my_printf(&huart1, "KEY3 Pressed - LED3 Toggle\r\n");
                    break;
                case 4: // PE3 - KEY4 控制 LED4
                    led_buf[3] ^= 1;  // 翻转LED4状态
                    my_printf(&huart1, "KEY4 Pressed - LED4 Toggle\r\n");
                    break;
                default:
                    my_printf(&huart1, "Unknown Key ID: %d\r\n", key_id);
                    break;
            }
            break;
            
        case EBTN_EVT_ONRELEASE: // 释放事件 (按键成功后触发一次)
            // 可以在这里添加按键释放时的处理逻辑
            break;
            
        case EBTN_EVT_ONCLICK: // 单击/点击事件 (按键释放后，或达到最大连续点击次数时触发)
            // 可以在这里添加单击事件的处理逻辑
            break;
            
        case EBTN_EVT_KEEPALIVE: // 保持活/长按事件 (按键持续时间超过阈值后，按键仍在持续)
            // 可以在这里添加长按事件的处理逻辑
            break;
            
        default: // 未知事件 (理论上不应出现)
            my_printf(&huart1, "Unknown Key Event: %d\r\n", evt);
            break;
    }
}


void jiguang(int i)
{
if(i==1)
{
	HAL_GPIO_WritePin(GPIOC,GPIO_PIN_5,GPIO_PIN_SET);
	
}
else if(i==0)
	{
	
HAL_GPIO_WritePin(GPIOC,GPIO_PIN_5,GPIO_PIN_RESET);
}




}