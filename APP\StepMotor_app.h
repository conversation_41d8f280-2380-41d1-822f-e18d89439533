/**
 * @file app_motor.h
 * @brief ����EmmV5�ĵ�����ƺ���
 * @copyright �״׵��ӹ�����
 */

#ifndef __STEPMOTOR_APP_H_
#define __STEPMOTOR_APP_H_

#include "MyDefine.h"

/* ������ƺ궨�� */
#define MOTOR_X_ADDR        0x01          // X������ַ
#define MOTOR_Y_ADDR        0x01          // Y������ַ
#define MOTOR_X_UART        huart2        // X�������� ��
#define MOTOR_Y_UART        huart4        // Y�������� ��
#define MOTOR_MAX_SPEED     3            // ������ת��(RPM)
#define MOTOR_ACCEL         0             // ������ٶ�(0��ʾֱ������)
#define MOTOR_SYNC_FLAG     false         // ���ͬ����־
#define MOTOR_MAX_ANGLE     50            // ������Ƕ�����(��50��)
void StepMotor_Move_Pulses_Speed(int32_t x_pulses, int32_t y_pulses, uint16_t speed_rpm);
/* �켣��ֵ���ض��� */
#define TRAJECTORY_BUFFER_SIZE  100          // �켣������С
#define TRAJECTORY_STEP_TIME    20           // �켣ִ�в���ʱ��(ms)

/* �켣�㽹���� */
typedef struct {
    int16_t x;                               // X����λ��
    int16_t y;                               // Y����λ��
    uint16_t speed;                          // ����ĵ��ٶȰ٧ֱ�
} TrajectoryPoint_t;

/* �켣���������� */
typedef struct {
    TrajectoryPoint_t points[TRAJECTORY_BUFFER_SIZE];  // �켣������
    uint16_t head;                           // ����ͷָ��
    uint16_t tail;                           // ����βָ��
    uint16_t count;                          // ��ǰ����
    bool is_executing;                       // �Ƿ���ִ���켣
} TrajectoryBuffer_t;

/* �������� */
void StepMotor_Init(void);                    // �����ʼ��
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent);  // ����XY����ٶ�(�ٷֱ�)
void StepMotor_Stop(void);                    // ֹͣ���е��

/* �켣��ֵ���� */
void StepMotor_Trajectory_Init(void);         // ��ʼ���켣ϵͳ
bool StepMotor_Move_To_Point(int16_t target_x, int16_t target_y, uint16_t speed_percent);  // �ƶ�����ָ���
void StepMotor_Trajectory_Execute(void);      // �켣ִ�к���(����ʱ����е���)
bool StepMotor_Is_Trajectory_Complete(void);  // ���켣�Ƿ�ִ�����
void StepMotor_Clear_Trajectory(void);        // ��ճ켣������
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses);

/* ========== 编码器增强功能API函数声明 ========== */
uint8_t StepMotor_Move_Pulses_Wait(int32_t x_pulses, int32_t y_pulses, uint32_t timeout_ms); // 带等待的脉冲运动 (返回值: 0-超时, 1-已到位, 2-通信错误)
void StepMotor_Move_Pulses_Async(int32_t x_pulses, int32_t y_pulses);                        // 异步脉冲运动（立即返回）

/* ========== 编码器相关API函数声明 ========== */
void StepMotor_Init_Encoder(void);                    // 编码器初始化
uint8_t StepMotor_Check_Ready(void);                  // 检查电机到位状态 (返回值: 0-运动中, 1-已到位, 2-通信错误)
void StepMotor_Clear_Ready_Flags(void);               // 清除电机到位标志
uint8_t StepMotor_Get_Motor_Status(uint8_t motor_id); // 获取指定电机状态 (motor_id: 1-X轴, 2-Y轴)
void Motor_X_Receive_Data(uint8_t com_data);          // X轴编码器数据处理函数
void Motor_Y_Receive_Data(uint8_t com_data);          // Y轴编码器数据处理函数

/* ========== 脉冲计数管理API函数声明 ========== */
int32_t StepMotor_Get_Pulse_Count(uint8_t motor_id);                                        // 获取指定电机的当前脉冲计数
int32_t StepMotor_Get_Target_Pulses(uint8_t motor_id);                                      // 获取指定电机的目标脉冲数
void StepMotor_Reset_Pulse_Count(uint8_t motor_id);                                         // 重置指定电机的脉冲计数
void StepMotor_Get_All_Pulse_Info(int32_t *x_current, int32_t *y_current,                  // 获取所有电机的脉冲信息
                                  int32_t *x_target, int32_t *y_target);
void StepMotor_Print_Pulse_Status(void);                                                    // 打印当前脉冲状态信息

/* ========== Y轴零点设置API函数声明 ========== */
void StepMotor_Y_Set_Current_As_Zero(void);                                                     // 将Y轴当前位置设置为零点
void StepMotor_Y_Return_To_Zero(void);                                                          // Y轴回零运动

#endif /* __APP_MOTOR_H_ */
